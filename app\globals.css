@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter);
  --font-serif: var(--font-playfair);
  --font-display: var(--font-cormorant);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.75rem;
  /* Premium Light Theme - Atlantis Real Estate */
  --background: oklch(0.99 0 0); /* Pure white with slight warmth */
  --foreground: oklch(0.15 0 0); /* Deep charcoal for text */
  --card: oklch(1 0 0); /* Pure white for cards */
  --card-foreground: oklch(0.15 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0 0);

  /* Primary: Deep Navy Blue for trust and professionalism */
  --primary: oklch(0.25 0.08 240); /* Deep navy blue */
  --primary-foreground: oklch(0.99 0 0);

  /* Secondary: Soft blue for interactive elements */
  --secondary: oklch(0.55 0.15 240); /* Soft blue */
  --secondary-foreground: oklch(0.99 0 0);

  /* Muted: Soft grays for subtle elements */
  --muted: oklch(0.97 0 0); /* Very light gray */
  --muted-foreground: oklch(0.45 0 0); /* Medium gray */

  /* Accent: Soft blue for interactive elements */
  --accent: oklch(0.55 0.15 240); /* Soft blue */
  --accent-foreground: oklch(0.99 0 0);

  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.9 0 0); /* Subtle borders */
  --input: oklch(0.95 0 0); /* Input backgrounds */
  --ring: oklch(0.55 0.15 240); /* Focus rings match accent */

  /* Custom Atlantis Colors */
  --atlantis-navy: oklch(0.25 0.08 240);
  --atlantis-blue: oklch(0.55 0.15 240);
  --atlantis-pearl: oklch(0.95 0.02 240);
  --atlantis-gray-50: oklch(0.98 0 0);
  --atlantis-gray-100: oklch(0.95 0 0);
  --atlantis-gray-200: oklch(0.9 0 0);
  --atlantis-gray-300: oklch(0.8 0 0);
  --atlantis-gray-400: oklch(0.65 0 0);
  --atlantis-gray-500: oklch(0.45 0 0);
  --atlantis-gray-600: oklch(0.35 0 0);
  --atlantis-gray-700: oklch(0.25 0 0);
  --atlantis-gray-800: oklch(0.15 0 0);
  --atlantis-gray-900: oklch(0.1 0 0);

  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer utilities {
  /* Premium gradient backgrounds */
  .bg-gradient-atlantis {
    background: linear-gradient(135deg, var(--atlantis-navy) 0%, var(--atlantis-blue) 100%);
  }

  .bg-gradient-blue {
    background: linear-gradient(135deg, var(--atlantis-blue) 0%, oklch(0.6 0.12 240) 100%);
  }

  .bg-gradient-royal {
    background: linear-gradient(135deg, var(--atlantis-navy) 0%, var(--atlantis-blue) 100%);
  }

  /* Text gradients */
  .text-gradient-atlantis {
    background: linear-gradient(135deg, var(--atlantis-navy) 0%, var(--atlantis-blue) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Glass morphism effects */
  .glass {
    backdrop-filter: blur(16px) saturate(180%);
    background-color: rgba(255, 255, 255, 0.75);
    border: 1px solid rgba(255, 255, 255, 0.125);
  }

  /* Premium shadows */
  .shadow-atlantis {
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  .shadow-atlantis-lg {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  /* Smooth transitions */
  .transition-atlantis {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Custom scrollbar */
  .scrollbar-atlantis::-webkit-scrollbar {
    width: 8px;
  }

  .scrollbar-atlantis::-webkit-scrollbar-track {
    background: var(--atlantis-gray-100);
  }

  .scrollbar-atlantis::-webkit-scrollbar-thumb {
    background: var(--atlantis-gray-300);
    border-radius: 4px;
  }

  .scrollbar-atlantis::-webkit-scrollbar-thumb:hover {
    background: var(--atlantis-gray-400);
  }
}
