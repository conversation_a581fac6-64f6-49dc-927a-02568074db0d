"use client";
import React from "react";
import { cn } from "@/lib/utils";

interface MovingBorderProps {
  children: React.ReactNode;
  duration?: number;
  rx?: string;
  ry?: string;
  className?: string;
  containerClassName?: string;
  borderClassName?: string;
  as?: React.ElementType;
}

export const MovingBorder = ({
  children,
  duration = 2000,
  rx,
  ry,
  className,
  containerClassName,
  borderClassName,
  as: Component = "button",
  ...otherProps
}: MovingBorderProps) => {
  return (
    <Component
      className={cn(
        "relative h-16 w-40 overflow-hidden rounded-xl p-[1px] focus:outline-none",
        containerClassName
      )}
      style={{
        background: `linear-gradient(90deg, var(--royal-blue-500), var(--royal-purple-500), var(--royal-gold-500), var(--royal-blue-500))`,
        backgroundSize: "400% 400%",
        animation: `gradient ${duration}ms linear infinite`,
      }}
      {...otherProps}
    >
      <div
        className={cn(
          "relative flex h-full w-full items-center justify-center rounded-xl bg-slate-900/[0.8] text-sm antialiased backdrop-blur-xl",
          className
        )}
      >
        {children}
      </div>
    </Component>
  );
};
